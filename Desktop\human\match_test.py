
from edge_detector import EdgeDetector
from motion_controller import MotionController

import time

class MatchTest:
    def __init__(self):
        self.edge_detector = EdgeDetector()
        self.motion_controller = MotionController()
    
    # 开始比赛
    def start_match(self):
        
        # 做出默认动作
        self.motion_controller.default_action()
        time.sleep(0.8)
        self.motion_controller.start_pose()
        time.sleep(0.1)

        # 遮挡一下adc3，使得其可以感应上台
        while True:
            ad3 = self.edge_detector.get_left_distance()
            if ad3 > 1000:
                break
            time.sleep(0.01)
        
        #前进上台
        self.motion_controller.move_cmd(300, 400)
        time.sleep(3)
        
        #做出默认动作
        self.motion_controller.default_action()
        
        #停止运动
        self.motion_controller.move_cmd(0, 0)
        time.sleep(2)
        
        #进入循环
        while True:
            
            #首先进行跌倒检测
            angle_state = self.edge_detector.angle_detect()
            
            #判断没有发生跌倒
            if angle_state == 1:
                
                # 检测当前是不是在擂台中央，判断敌人方位
                edge = self.edge_detector.enemy_detect()
                
                #在舞台中央，没有识别到任何东西，前进一段距离然后自旋
                if edge == 1:
                    self.k += 1
                    if self.k > 350:
                        self.motion_controller.move_cmd(300, -300)
                        time.sleep(0.01)
                        self.k = 0
                    else:
                        self.motion_controller.move_cmd(300, 300)
                        time.sleep(0.01)
                    self.motion_controller.default_action()
                
                #灰度检测到在擂台边缘，作如下处理：
                if edge == 2:
                    self.r += 1
                    #先立刻停止，然后后退，然后自转
                    if self.r < 4:
                        self.motion_controller.move_cmd(0, 0)
                        time.sleep(0.5)
                        self.motion_controller.move_cmd(-350, -350)
                        time.sleep(0.5)
                        self.motion_controller.move_cmd(300, -300)
                        time.sleep(1.3)
                        self.motion_controller.move_cmd(0, 0)
                        time.sleep(0.01)
                        self.motion_controller.default_action()
                    #重复后退，自转，停止的过程
                    elif 3 < self.r < 9:
                        self.motion_controller.move_cmd(-350, -350)
                        time.sleep(0.5)
                        self.motion_controller.move_cmd(300, -300)
                        time.sleep(1.3)
                        self.motion_controller.move_cmd(0, 0)
                        time.sleep(0.01)
                        self.motion_controller.default_action()                        
                    elif self.r > 12:
                        self.r = 0
                    #轻微后退，自转，停止
                    else:
                        self.motion_controller.move_cmd(-350, -350)
                        time.sleep(0.5)
                        self.motion_controller.move_cmd(-300, 300)
                        time.sleep(0.5)
                        self.motion_controller.move_cmd(0, 0)
                        time.sleep(0.01)
                        self.motion_controller.default_action()
                        
                # 检测到左侧有敌人，后退，左转，前进，停止，如果前方有敌人，攻击
                if edge == 4:
                    self.motion_controller.move_cmd(-300, -300)
                    time.sleep(0.45)
                    self.motion_controller.move_cmd(-300, 300)
                    time.sleep(0.35)
                    self.motion_controller.move_cmd(300, 300)
                    time.sleep(0.35)
                    self.motion_controller.move_cmd(0, 0)
                    time.sleep(0.01)
                    
                    ad6 = self.edge_detector.get_front_distance()
                    if ad6 > 2000:
                        self.motion_controller.move_cmd(300, 300)
                        self.motion_controller.attack_b()
                        time.sleep(1.4)
                        self.motion_controller.move_cmd(0, 0)
                        
                # 检测到前右侧有敌人，右转，前进，停止，攻击
                if edge == 5:
                    self.motion_controller.move_cmd(300, -300)
                    time.sleep(0.1)
                    self.motion_controller.move_cmd(400, 400)
                    time.sleep(0.35)
                    self.motion_controller.move_cmd(0, 0)
                    time.sleep(0.01)
                    
                    ad6 = self.edge_detector.get_front_distance()
                    if ad6 > 2000:
                        self.motion_controller.move_cmd(300, 300)
                        self.motion_controller.attack_b()
                        time.sleep(1.4)
                        self.motion_controller.move_cmd(0, 0)

                #  正前方有敌人，攻击，前进
                if edge == 7:
                    self.motion_controller.attack_a()
                    self.motion_controller.move_cmd(300, 300)
                    time.sleep(1.4)
                    self.motion_controller.move_cmd(0, 0)
                    
            # 向前倾倒，执行前撑起身动作
            if angle_state == 2:
                self.nq += 1
                if self.nq == 6:
                    self.motion_controller.move_cmd(0, 0)
                    time.sleep(0.1)
                    self.motion_controller.default_action()
                    time.sleep(0.8)
                    self.motion_controller.ahead_dump()
                    time.sleep(0.5)
                    self.nq = 0
                else:
                    time.sleep(0.04)
                    
            # 向后倾倒，执行后撑起身动作
            if angle_state == 3:
                self.nh += 1
                if self.nh == 6:
                    self.motion_controller.move_cmd(0, 0)
                    time.sleep(0.1)
                    self.motion_controller.default_action()
                    time.sleep(0.8)
                    self.motion_controller.behind_dump()
                    time.sleep(0.5)
                    self.nh = 0
                else:
                    time.sleep(0.05)
            
            #倾倒状态不明，轻微向前移动
            if angle_state == 0:
                self.motion_controller.move_cmd(200, 200)
                time.sleep(0.01)  

if __name__ == '__main__':
    match = MatchTest()
    match.start_match()    
        