# 仿人散打赛项机器人控制代码

基于perfect1.py改写的仿人散打赛项机器人控制代码，适用于装有摄像头、灰度传感器、倾角传感器和胳膊舵机的仿人机器人。

## 硬件配置

### 传感器配置
- **灰度传感器**: 
  - 左侧灰度传感器: ADC通道0
  - 右侧灰度传感器: ADC通道1
- **倾角传感器**: ADC通道2
- **摄像头**: 用于AprilTag检测

### 舵机配置
- **左胳膊舵机**:
  - 4号舵机: 前后运动 (数值增大=向前)
  - 5号舵机: 上下运动 (数值增大=向下)
  - 6号舵机: 上下运动 (数值增大=向下)
- **右胳膊舵机**:
  - 7号舵机: 前后运动 (数值增大=向后)
  - 8号舵机: 上下运动 (数值增大=向下)
  - 9号舵机: 上下运动 (数值增大=向下)

## AprilTag配置

敌方机器人四个方向贴有AprilTag标签（Tag36h11格式）:
- **ID 1**: 敌方机器人正前方
- **ID 2**: 敌方机器人左侧
- **ID 3**: 敌方机器人右侧
- **ID 4**: 敌方机器人正后方

## 功能特性

### 1. 灰度传感器导航
- 保持在灰度值1600-1800范围内行走
- 自动避开擂台边缘（灰度值<1400）
- 根据灰度差异调整行走方向

### 2. 倾角传感器平衡控制
- 实时监测机器人倾倒状态
- 向前倾倒时：双臂向前支撑
- 向后倾倒时：双臂向后支撑
- 自动恢复平衡后继续比赛

### 3. 敌方机器人检测与攻击
- 使用OpenCV内置AprilTag检测器
- 根据检测到的标签ID判断敌方朝向
- 实施相应的攻击策略：
  - ID 1 (正面): 直接冲击
  - ID 2 (左侧): 左转后攻击
  - ID 3 (右侧): 右转后攻击
  - ID 4 (背面): 直接攻击（最佳时机）

### 4. 智能控制逻辑
- 优先级控制：平衡 > 攻击 > 导航
- 多线程摄像头处理
- 异常处理和资源清理

## 使用方法

### 1. 运行主程序
```bash
python humanoid_fighting_robot.py
```

### 2. 启动流程
1. 程序初始化机器人和传感器
2. 等待软启动信号（遮挡两个灰度传感器）
3. 自动离开出发区
4. 开始主控制循环

### 3. 测试程序
```bash
python test_humanoid_robot.py
```

测试选项：
- 传感器测试：检查各传感器读数
- 胳膊动作测试：测试平衡支撑动作
- 导航功能测试：测试灰度导航
- 敌方检测测试：测试AprilTag检测
- 完整功能测试：运行完整控制逻辑

## 参数调整

### 灰度传感器阈值
```python
GRAY_TARGET_MIN = 1600      # 目标灰度值下限
GRAY_TARGET_MAX = 1800      # 目标灰度值上限
GRAY_EDGE_THRESHOLD = 1400  # 边缘检测阈值
```

### 倾角传感器阈值
```python
TILT_FORWARD_THRESHOLD = 2200   # 向前倾倒阈值
TILT_BACKWARD_THRESHOLD = 1800  # 向后倾倒阈值
```

### 速度设置
```python
NORMAL_SPEED = 400    # 正常巡航速度
ATTACK_SPEED = 600    # 攻击速度
TURN_SPEED = 350      # 转向速度
```

### 舵机角度
```python
ARM_NEUTRAL_ANGLE = 512     # 中性位置
ARM_FORWARD_ANGLE = 700     # 向前支撑角度
ARM_BACKWARD_ANGLE = 300    # 向后支撑角度
```

## 比赛策略

1. **开局**: 快速离开出发区，进入比赛区域中心
2. **巡航**: 在灰度1600-1800范围内寻找敌方机器人
3. **攻击**: 检测到敌方后根据朝向选择最佳攻击角度
4. **防守**: 实时监控平衡状态，防止被推倒
5. **边缘处理**: 避免掉出擂台

## 注意事项

1. 确保摄像头能清晰识别AprilTag标签
2. 根据实际硬件调整传感器阈值
3. 测试舵机角度范围，避免机械冲突
4. 比赛前充分测试各项功能
5. 注意擂台尺寸限制（1.2m x 1.2m）

## 依赖库

- OpenCV (cv2)
- NumPy
- threading
- time
- uptech (硬件控制库)
- motion_controller (运动控制库)

## 故障排除

1. **摄像头无法打开**: 检查摄像头连接和权限
2. **AprilTag检测失败**: 检查光照条件和标签质量
3. **传感器读数异常**: 检查ADC通道连接
4. **舵机不动作**: 检查舵机模式和角度范围
5. **运动控制异常**: 检查motion_controller库
