from motion_controller import MotionController
import time

motion = MotionController()

print("Start Pose")
motion.start_pose()
time.sleep(3.0)

print("Default Action")
motion.default_action()
time.sleep(3.0)

print("Attack A")
motion.attack_a()
time.sleep(3.0)

print("Attack B")
motion.attack_b()
time.sleep(3.0)

print("Attack TAG")
motion.attack_tag()
time.sleep(3.0)

print("Ahead Dump")
motion.ahead_dump()
time.sleep(3.0)

print("Behind Dump")
motion.behind_dump()
time.sleep(3.0)

print("Test Test")