import sys
sys.path.append("..")

from uptech import UpTech
from motion_controller import MotionController

import cv2
import threading
import time
import numpy as np

"""
仿人散打赛项机器人控制代码

传感器配置说明:
1. 灰度传感器(ADC通道):
   - 左侧灰度传感器: ADC通道0
   - 右侧灰度传感器: ADC通道1

2. 倾角传感器(ADC通道):
   - 倾角传感器: ADC通道2

3. 摄像头: 用于AprilTag检测
   - 敌方机器人前方: AprilTag ID 1
   - 敌方机器人左侧: AprilTag ID 2  
   - 敌方机器人右侧: AprilTag ID 3
   - 敌方机器人后方: AprilTag ID 4

4. 舵机配置:
   - 左胳膊舵机: 4(前后), 5(上下), 6(上下)
   - 右胳膊舵机: 7(前后), 8(上下), 9(上下)
   
舵机运动方向:
- 4号舵机数值增大: 向前转
- 5号舵机数值增大: 向下转  
- 6号舵机数值增大: 向下转
- 7号舵机数值增大: 向后转
- 8号舵机数值增大: 向下转
- 9号舵机数值增大: 向下转
"""

class HumanoidFightingRobot:
    # 灰度传感器阈值设置
    GRAY_TARGET_MIN = 1600  # 目标灰度值下限
    GRAY_TARGET_MAX = 1800  # 目标灰度值上限
    GRAY_EDGE_THRESHOLD = 1400  # 边缘检测阈值，低于此值认为接近边缘
    
    # 倾角传感器阈值设置
    TILT_FORWARD_THRESHOLD = 2200  # 向前倾倒阈值
    TILT_BACKWARD_THRESHOLD = 1800  # 向后倾倒阈值
    TILT_NORMAL_MIN = 1900  # 正常倾角范围下限
    TILT_NORMAL_MAX = 2100  # 正常倾角范围上限
    
    # 速度设置
    NORMAL_SPEED = 400      # 正常巡航速度
    ATTACK_SPEED = 600      # 攻击速度
    TURN_SPEED = 350        # 转向速度
    RETREAT_SPEED = 300     # 后退速度
    
    # 舵机角度设置
    ARM_NEUTRAL_ANGLE = 512   # 胳膊中性位置
    ARM_FORWARD_ANGLE = 700   # 胳膊向前支撑角度
    ARM_BACKWARD_ANGLE = 300  # 胳膊向后支撑角度
    ARM_DOWN_ANGLE = 700      # 胳膊向下角度
    ARM_UP_ANGLE = 300        # 胳膊向上角度
    SERVO_SPEED = 512         # 舵机转动速度
    
    # 时间设置
    TURN_TIME = 0.8          # 90度转向时间
    BALANCE_TIME = 1.0       # 平衡支撑时间
    ATTACK_TIME = 2.0        # 攻击持续时间

    def __init__(self):
        """初始化仿人散打机器人"""
        print("初始化仿人散打机器人...")
        
        # 初始化UpTech控制器
        self.uptech = UpTech()
        # 打开ADC和IO通道
        self.uptech.ADC_IO_Open()
        # 初始化CDS舵机控制
        self.uptech.CDS_Open()
        time.sleep(0.5)
        
        # 设置所有胳膊舵机为位置模式
        for servo_id in [4, 5, 6, 7, 8, 9]:
            self.uptech.CDS_SetMode(servo_id, 0)
            time.sleep(0.1)
        
        # 初始化运动控制器
        self.motion_controller = MotionController()
        # 确保电机停止
        self.motion_controller.move_cmd(0, 0)
        time.sleep(0.1)
        
        # 初始化AprilTag检测器 - 使用OpenCV内置检测器
        try:
            # 尝试使用新版本OpenCV的API
            self.detector = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_APRILTAG_36h11)
            self.parameters = cv2.aruco.DetectorParameters()
        except AttributeError:
            # 如果新API不可用，使用旧版本API
            self.detector = cv2.aruco.Dictionary_get(cv2.aruco.DICT_APRILTAG_36h11)
            self.parameters = cv2.aruco.DetectorParameters_create()
        
        # 初始化摄像头线程
        self.camera_thread = threading.Thread(target=self.camera_loop)
        self.camera_activate = True
        self.camera_thread.daemon = True
        self.camera_thread.start()
        
        # 初始化检测结果
        self.detected_tag_id = None
        self.tag_center_x = None
        self.tag_distance = None
        
        # 初始化胳膊到中性位置
        self.reset_arms()
        
        print("仿人散打机器人初始化完成")

    def camera_loop(self):
        """摄像头线程，用于AprilTag检测"""
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("Failed to open camera")
            return

        while self.camera_activate:
            ret, frame = cap.read()
            if not ret:
                print("Failed to read frame")
                break

            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 使用OpenCV内置AprilTag检测器
            try:
                # 尝试使用新版本API
                corners, ids, _ = cv2.aruco.detectMarkers(gray, self.detector, parameters=self.parameters)
            except:
                # 如果新API失败，尝试旧版本API
                corners, ids, _ = cv2.aruco.detectMarkers(gray, self.detector, parameters=self.parameters)

            if ids is not None and len(ids) > 0:
                # 找到最大的标签（距离最近）
                max_area = 0
                best_id = None
                best_center_x = None
                best_distance = None
                
                for i, tag_id in enumerate(ids.flatten()):
                    if tag_id in [1, 2, 3, 4]:  # 只处理敌方机器人的标签
                        corner = corners[i][0]
                        # 计算标签面积（用于估算距离）
                        area = cv2.contourArea(corner)
                        if area > max_area:
                            max_area = area
                            best_id = tag_id
                            # 计算标签中心X坐标
                            best_center_x = int(np.mean(corner[:, 0]))
                            # 估算距离（面积越大距离越近）
                            best_distance = 10000 / (area + 1)  # 简单的距离估算
                
                if best_id is not None:
                    self.detected_tag_id = best_id
                    self.tag_center_x = best_center_x
                    self.tag_distance = best_distance
                    print(f"检测到敌方机器人标签 ID: {best_id}, 中心X: {best_center_x}, 距离: {best_distance:.1f}")
                else:
                    self.detected_tag_id = None
                    self.tag_center_x = None
                    self.tag_distance = None
            else:
                self.detected_tag_id = None
                self.tag_center_x = None
                self.tag_distance = None

            # 绘制检测结果
            if corners is not None and ids is not None:
                cv2.aruco.drawDetectedMarkers(frame, corners, ids)

            cv2.imshow("AprilTag Detection", frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                self.camera_activate = False
                break

        cap.release()
        cv2.destroyAllWindows()

    def reset_arms(self):
        """重置胳膊到中性位置"""
        print("重置胳膊到中性位置")
        # 左胳膊 (4, 5, 6)
        self.uptech.CDS_SetAngle(4, self.ARM_NEUTRAL_ANGLE, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(5, self.ARM_NEUTRAL_ANGLE, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(6, self.ARM_NEUTRAL_ANGLE, self.SERVO_SPEED)
        
        # 右胳膊 (7, 8, 9)
        self.uptech.CDS_SetAngle(7, self.ARM_NEUTRAL_ANGLE, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(8, self.ARM_NEUTRAL_ANGLE, self.SERVO_SPEED)
        self.uptech.CDS_SetAngle(9, self.ARM_NEUTRAL_ANGLE, self.SERVO_SPEED)
        
        time.sleep(0.5)

    def get_gray_values(self):
        """获取灰度传感器数值"""
        left_gray = self.uptech.ADC_Get_Channel(0)   # 左侧灰度传感器
        right_gray = self.uptech.ADC_Get_Channel(1)  # 右侧灰度传感器
        return left_gray, right_gray

    def get_tilt_value(self):
        """获取倾角传感器数值"""
        tilt_value = self.uptech.ADC_Get_Channel(2)  # 倾角传感器
        return tilt_value

    def check_balance(self):
        """检查机器人平衡状态并进行平衡控制"""
        tilt_value = self.get_tilt_value()
        print(f"倾角传感器值: {tilt_value}")

        if tilt_value > self.TILT_FORWARD_THRESHOLD:
            # 向前倾倒，胳膊向前支撑
            print("检测到向前倾倒，胳膊向前支撑")
            self.arms_forward_support()
            return "forward_tilt"
        elif tilt_value < self.TILT_BACKWARD_THRESHOLD:
            # 向后倾倒，胳膊向后支撑
            print("检测到向后倾倒，胳膊向后支撑")
            self.arms_backward_support()
            return "backward_tilt"
        elif self.TILT_NORMAL_MIN <= tilt_value <= self.TILT_NORMAL_MAX:
            # 正常状态
            return "normal"
        else:
            # 轻微倾斜，保持当前状态
            return "slight_tilt"

    def arms_forward_support(self):
        """胳膊向前支撑"""
        print("执行胳膊向前支撑")
        # 左胳膊向前支撑 (4号舵机增大=向前, 5,6号舵机增大=向下)
        self.uptech.CDS_SetAngle(4, self.ARM_FORWARD_ANGLE, self.SERVO_SPEED)  # 向前
        self.uptech.CDS_SetAngle(5, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下
        self.uptech.CDS_SetAngle(6, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下

        # 右胳膊向前支撑 (7号舵机增大=向后，所以要减小来向前, 8,9号舵机增大=向下)
        self.uptech.CDS_SetAngle(7, self.ARM_BACKWARD_ANGLE, self.SERVO_SPEED) # 向前(对于右臂)
        self.uptech.CDS_SetAngle(8, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下
        self.uptech.CDS_SetAngle(9, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下

        time.sleep(self.BALANCE_TIME)

    def arms_backward_support(self):
        """胳膊向后支撑"""
        print("执行胳膊向后支撑")
        # 左胳膊向后支撑 (4号舵机减小=向后, 5,6号舵机增大=向下)
        self.uptech.CDS_SetAngle(4, self.ARM_BACKWARD_ANGLE, self.SERVO_SPEED) # 向后
        self.uptech.CDS_SetAngle(5, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下
        self.uptech.CDS_SetAngle(6, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下

        # 右胳膊向后支撑 (7号舵机增大=向后, 8,9号舵机增大=向下)
        self.uptech.CDS_SetAngle(7, self.ARM_FORWARD_ANGLE, self.SERVO_SPEED)  # 向后
        self.uptech.CDS_SetAngle(8, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下
        self.uptech.CDS_SetAngle(9, self.ARM_DOWN_ANGLE, self.SERVO_SPEED)     # 向下

        time.sleep(self.BALANCE_TIME)

    def navigate_by_gray(self):
        """基于灰度传感器的导航控制"""
        left_gray, right_gray = self.get_gray_values()
        avg_gray = (left_gray + right_gray) / 2

        print(f"灰度传感器值 - 左: {left_gray}, 右: {right_gray}, 平均: {avg_gray:.1f}")

        # 检查是否接近边缘
        if left_gray < self.GRAY_EDGE_THRESHOLD or right_gray < self.GRAY_EDGE_THRESHOLD:
            print("检测到边缘，后退并转向")
            # 后退
            self.motion_controller.move_cmd(-self.RETREAT_SPEED, -self.RETREAT_SPEED)
            time.sleep(0.5)
            # 转向远离边缘
            if left_gray < right_gray:
                # 左侧更接近边缘，右转
                self.motion_controller.move_cmd(self.TURN_SPEED, -self.TURN_SPEED)
            else:
                # 右侧更接近边缘，左转
                self.motion_controller.move_cmd(-self.TURN_SPEED, self.TURN_SPEED)
            time.sleep(self.TURN_TIME)
            return "edge_avoidance"

        # 在目标灰度范围内
        if self.GRAY_TARGET_MIN <= avg_gray <= self.GRAY_TARGET_MAX:
            return "in_target_zone"

        # 灰度值太高，向边缘移动
        elif avg_gray > self.GRAY_TARGET_MAX:
            print("灰度值过高，向边缘移动")
            # 根据左右灰度差异调整方向
            if left_gray > right_gray:
                # 左侧灰度更高，向左移动
                self.motion_controller.move_cmd(-self.TURN_SPEED, self.TURN_SPEED)
            else:
                # 右侧灰度更高，向右移动
                self.motion_controller.move_cmd(self.TURN_SPEED, -self.TURN_SPEED)
            time.sleep(0.3)
            return "move_to_edge"

        # 灰度值太低，向中心移动
        elif avg_gray < self.GRAY_TARGET_MIN:
            print("灰度值过低，向中心移动")
            # 向前移动到灰度更高的区域
            self.motion_controller.move_cmd(self.NORMAL_SPEED, self.NORMAL_SPEED)
            time.sleep(0.3)
            return "move_to_center"

        return "normal_navigation"

    def detect_enemy(self):
        """检测敌方机器人"""
        if self.detected_tag_id is not None:
            return {
                'detected': True,
                'tag_id': self.detected_tag_id,
                'center_x': self.tag_center_x,
                'distance': self.tag_distance
            }
        return {'detected': False}

    def attack_enemy(self, enemy_info):
        """攻击敌方机器人"""
        tag_id = enemy_info['tag_id']
        center_x = enemy_info['center_x']
        distance = enemy_info['distance']

        print(f"攻击敌方机器人 - ID: {tag_id}, 位置: {center_x}, 距离: {distance:.1f}")

        # 根据标签ID判断敌方机器人的朝向
        if tag_id == 1:
            # 敌方机器人正面，直接攻击
            print("敌方机器人正面，直接冲击攻击")
            self.motion_controller.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
            time.sleep(self.ATTACK_TIME)
        elif tag_id == 2:
            # 敌方机器人左侧，需要调整角度攻击侧面
            print("敌方机器人左侧，调整角度攻击")
            # 稍微左转对准敌方
            self.motion_controller.move_cmd(-self.TURN_SPEED, self.TURN_SPEED)
            time.sleep(0.3)
            # 冲击攻击
            self.motion_controller.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
            time.sleep(self.ATTACK_TIME)
        elif tag_id == 3:
            # 敌方机器人右侧，需要调整角度攻击侧面
            print("敌方机器人右侧，调整角度攻击")
            # 稍微右转对准敌方
            self.motion_controller.move_cmd(self.TURN_SPEED, -self.TURN_SPEED)
            time.sleep(0.3)
            # 冲击攻击
            self.motion_controller.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
            time.sleep(self.ATTACK_TIME)
        elif tag_id == 4:
            # 敌方机器人背面，最佳攻击时机
            print("敌方机器人背面，最佳攻击时机")
            self.motion_controller.move_cmd(self.ATTACK_SPEED, self.ATTACK_SPEED)
            time.sleep(self.ATTACK_TIME)

        # 攻击后停止
        self.motion_controller.move_cmd(0, 0)

    def start(self):
        """主控制循环"""
        print("仿人散打机器人开始运行")
        self.motion_controller.move_cmd(0, 0)
        time.sleep(1)

        # 初始化胳膊位置
        self.reset_arms()
        time.sleep(0.5)

        while True:
            try:
                # 1. 优先检查平衡状态
                balance_status = self.check_balance()
                if balance_status in ["forward_tilt", "backward_tilt"]:
                    # 如果检测到倾倒，优先处理平衡，然后继续循环
                    print(f"处理平衡状态: {balance_status}")
                    time.sleep(0.5)
                    # 平衡后重置胳膊
                    self.reset_arms()
                    continue

                # 2. 检测敌方机器人
                enemy_info = self.detect_enemy()
                if enemy_info['detected']:
                    print(f"发现敌方机器人: {enemy_info}")
                    # 攻击敌方机器人
                    self.attack_enemy(enemy_info)
                    # 攻击后短暂停顿
                    time.sleep(0.5)
                    continue

                # 3. 基于灰度传感器导航
                nav_status = self.navigate_by_gray()
                print(f"导航状态: {nav_status}")

                # 4. 如果在目标区域内且没有敌人，进行巡航
                if nav_status == "in_target_zone":
                    print("在目标区域内巡航")
                    self.motion_controller.move_cmd(self.NORMAL_SPEED, self.NORMAL_SPEED)
                    time.sleep(0.2)

                # 短暂延时避免过于频繁的传感器读取
                time.sleep(0.1)

            except Exception as e:
                print(f"主循环异常: {e}")
                # 发生异常时停止运动并重置胳膊
                self.motion_controller.move_cmd(0, 0)
                self.reset_arms()
                time.sleep(0.5)

    def cleanup(self):
        """清理资源"""
        print("清理资源...")
        self.camera_activate = False
        if self.camera_thread.is_alive():
            self.camera_thread.join(timeout=2)
        self.motion_controller.move_cmd(0, 0)
        self.reset_arms()
        cv2.destroyAllWindows()

def main():
    """主函数"""
    robot = HumanoidFightingRobot()
    print("仿人散打机器人初始化完成")

    # 等待系统稳定
    time.sleep(2)

    # 读取几次传感器值，清除可能的初始异常值
    for _ in range(5):
        robot.get_gray_values()
        robot.get_tilt_value()
        time.sleep(0.1)

    # 等待软启动触发 - 使用灰度传感器作为启动信号
    print("等待软启动触发，请遮挡灰度传感器")
    while True:
        left_gray, right_gray = robot.get_gray_values()
        print(f"灰度传感器值 - 左: {left_gray}, 右: {right_gray}")

        # 当两个灰度传感器都被遮挡时启动（数值显著降低）
        if left_gray < 500 and right_gray < 500:
            print("软启动触发")
            print("机器人准备开始运行")

            # 离开出发区 - 向后移动进入比赛区域
            print("离开出发区...")
            robot.motion_controller.move_cmd(-robot.NORMAL_SPEED, -robot.NORMAL_SPEED)
            time.sleep(2.0)  # 后退2秒离开出发区

            # 停止移动
            robot.motion_controller.move_cmd(0, 0)
            time.sleep(0.5)
            break
        time.sleep(0.1)

    try:
        # 开始主控制循环
        robot.start()
    except KeyboardInterrupt:
        print("程序被用户中断")
    finally:
        robot.cleanup()

if __name__ == "__main__":
    main()
