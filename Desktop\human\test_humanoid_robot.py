#!/usr/bin/env python3
"""
仿人散打机器人测试脚本
用于测试各个功能模块
"""

import sys
import time
from humanoid_fighting_robot import HumanoidFightingRobot

def test_sensors(robot):
    """测试传感器读取"""
    print("=== 传感器测试 ===")
    
    for i in range(10):
        # 测试灰度传感器
        left_gray, right_gray = robot.get_gray_values()
        print(f"灰度传感器 - 左: {left_gray}, 右: {right_gray}")
        
        # 测试倾角传感器
        tilt_value = robot.get_tilt_value()
        print(f"倾角传感器: {tilt_value}")
        
        # 测试敌方检测
        enemy_info = robot.detect_enemy()
        if enemy_info['detected']:
            print(f"检测到敌方: ID={enemy_info['tag_id']}, X={enemy_info['center_x']}, 距离={enemy_info['distance']:.1f}")
        else:
            print("未检测到敌方")
        
        print("-" * 50)
        time.sleep(1)

def test_arm_movements(robot):
    """测试胳膊动作"""
    print("=== 胳膊动作测试 ===")
    
    print("重置胳膊到中性位置")
    robot.reset_arms()
    time.sleep(2)
    
    print("测试向前支撑")
    robot.arms_forward_support()
    time.sleep(2)
    
    print("重置胳膊")
    robot.reset_arms()
    time.sleep(2)
    
    print("测试向后支撑")
    robot.arms_backward_support()
    time.sleep(2)
    
    print("重置胳膊")
    robot.reset_arms()
    time.sleep(1)

def test_navigation(robot):
    """测试导航功能"""
    print("=== 导航功能测试 ===")
    
    for i in range(20):
        nav_status = robot.navigate_by_gray()
        print(f"导航状态: {nav_status}")
        
        # 检查平衡
        balance_status = robot.check_balance()
        if balance_status in ["forward_tilt", "backward_tilt"]:
            print(f"检测到倾倒: {balance_status}")
            robot.reset_arms()
        
        time.sleep(0.5)
    
    # 停止运动
    robot.motion_controller.move_cmd(0, 0)

def test_enemy_detection(robot):
    """测试敌方检测和攻击"""
    print("=== 敌方检测测试 ===")
    print("请在摄像头前放置AprilTag标签进行测试")
    
    for i in range(30):
        enemy_info = robot.detect_enemy()
        if enemy_info['detected']:
            print(f"检测到敌方机器人:")
            print(f"  标签ID: {enemy_info['tag_id']}")
            print(f"  中心X坐标: {enemy_info['center_x']}")
            print(f"  估算距离: {enemy_info['distance']:.1f}")
            
            # 模拟攻击（不实际移动）
            print(f"  将执行攻击策略...")
            
        else:
            print("未检测到敌方机器人")
        
        time.sleep(0.5)

def main():
    """主测试函数"""
    print("仿人散打机器人测试程序")
    print("请选择测试项目:")
    print("1. 传感器测试")
    print("2. 胳膊动作测试")
    print("3. 导航功能测试")
    print("4. 敌方检测测试")
    print("5. 完整功能测试")
    print("0. 退出")
    
    try:
        choice = input("请输入选择 (0-5): ").strip()
        
        if choice == '0':
            print("退出测试程序")
            return
        
        print("初始化机器人...")
        robot = HumanoidFightingRobot()
        time.sleep(2)
        
        try:
            if choice == '1':
                test_sensors(robot)
            elif choice == '2':
                test_arm_movements(robot)
            elif choice == '3':
                test_navigation(robot)
            elif choice == '4':
                test_enemy_detection(robot)
            elif choice == '5':
                print("开始完整功能测试...")
                print("按Ctrl+C停止测试")
                robot.start()
            else:
                print("无效选择")
        
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        
        finally:
            print("清理资源...")
            robot.cleanup()
    
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
