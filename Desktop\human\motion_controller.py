import sys
sys.path.append("..")

from uptech import UpTech
import time

"""
上半身动作控制器，封装仿人散打过程中用到的攻击，防御，起立等各个动作
"""

class MotionController:
    
    def __init__(self):
        
        # 初始化硬件通信
        self.uptech = UpTech()
        
        # 打开舵机通信
        self.uptech.CDS_Open()
        
        #竞赛总计使用8个舵机，3,4,5,6,7,8,11,12，注意配置好ID
        self.uptech.CDS_SetMode(3, 0)
        self.uptech.CDS_SetMode(4, 0)
        self.uptech.CDS_SetMode(5, 0)
        self.uptech.CDS_SetMode(6, 0)
        self.uptech.CDS_SetMode(7, 0)  
        self.uptech.CDS_SetMode(8, 0)     
        self.uptech.CDS_SetMode(11, 0)     
        self.uptech.CDS_SetMode(12, 0)  

    # 速度指令,参数分别为左速度和右速度，自由控制-开环控制器
    def move_cmd(self, left_speed, right_speed):
        self.uptech.CDS_SetSpeed(1, left_speed)
        self.uptech.CDS_SetSpeed(2, -right_speed)
    
    # 在台上待机默认动作
    def default_action(self):
        self.uptech.CDS_SetAngle(3, 512, 512)
        self.uptech.CDS_SetAngle(4, 75, 512)
        self.uptech.CDS_SetAngle(5, 307, 512)
        self.uptech.CDS_SetAngle(6, 512, 512)
        self.uptech.CDS_SetAngle(7, 949, 512)
        self.uptech.CDS_SetAngle(8, 717, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)         

    # 前倾倒起立动作，适用于向前倾倒时站起
    def ahead_dump(self):
        self.uptech.CDS_SetAngle(3, 512, 512)
        self.uptech.CDS_SetAngle(4, 75, 512)
        self.uptech.CDS_SetAngle(5, 307, 512)
        self.uptech.CDS_SetAngle(6, 512, 512)
        self.uptech.CDS_SetAngle(7, 949, 512)
        self.uptech.CDS_SetAngle(8, 717, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)
        time.sleep(1.5)
        self.uptech.CDS_SetAngle(3, 82, 812)
        self.uptech.CDS_SetAngle(6, 942, 812)
        self.uptech.CDS_SetAngle(11, 462, 512)
        self.uptech.CDS_SetAngle(12, 562, 512)
        time.sleep(2)
        self.uptech.CDS_SetAngle(4, 375, 512)
        self.uptech.CDS_SetAngle(7, 649, 512)
        time.sleep(0.5)
        self.uptech.CDS_SetAngle(3, 112, 812)
        self.uptech.CDS_SetAngle(4, 512, 512)
        self.uptech.CDS_SetAngle(5, 512, 512)
        self.uptech.CDS_SetAngle(6, 912, 812)
        self.uptech.CDS_SetAngle(7, 512, 512)
        self.uptech.CDS_SetAngle(8, 512, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)
        time.sleep(2)
        self.uptech.CDS_SetAngle(3, 262, 812)
        self.uptech.CDS_SetAngle(6, 762, 812)
        self.uptech.CDS_SetAngle(11, 712, 512)
        self.uptech.CDS_SetAngle(12, 312, 512)
        time.sleep(1.5)
        self.uptech.CDS_SetAngle(5, 312, 812)
        self.uptech.CDS_SetAngle(8, 712, 812)
        time.sleep(0.5)
        self.uptech.CDS_SetAngle(3, 512, 512)
        self.uptech.CDS_SetAngle(4, 75, 512)
        self.uptech.CDS_SetAngle(5, 307, 512)
        self.uptech.CDS_SetAngle(6, 512, 512)
        self.uptech.CDS_SetAngle(7, 949, 512)
        self.uptech.CDS_SetAngle(8, 717, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)

    # 后倾倒起立动作，适用于向后倾倒时站起
    def behind_dump(self):
        self.uptech.CDS_SetAngle(3, 512, 512)
        self.uptech.CDS_SetAngle(4, 75, 512)
        self.uptech.CDS_SetAngle(5, 307, 512)
        self.uptech.CDS_SetAngle(6, 512, 512)
        self.uptech.CDS_SetAngle(7, 949, 512)
        self.uptech.CDS_SetAngle(8, 717, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)
        time.sleep(1.5)
        self.uptech.CDS_SetAngle(3, 942, 812)
        self.uptech.CDS_SetAngle(6, 82, 812)
        self.uptech.CDS_SetAngle(11, 562, 512)
        self.uptech.CDS_SetAngle(12, 462, 512)
        time.sleep(2)
        self.uptech.CDS_SetAngle(4, 375, 512)
        self.uptech.CDS_SetAngle(7, 649, 512)
        time.sleep(0.5)
        self.uptech.CDS_SetAngle(3, 912, 812)
        self.uptech.CDS_SetAngle(4, 512, 512)
        self.uptech.CDS_SetAngle(5, 512, 512)
        self.uptech.CDS_SetAngle(6, 112, 812)
        self.uptech.CDS_SetAngle(7, 512, 512)
        self.uptech.CDS_SetAngle(8, 512, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)
        time.sleep(2)
        self.uptech.CDS_SetAngle(3, 762, 812)
        self.uptech.CDS_SetAngle(6, 262, 812)
        self.uptech.CDS_SetAngle(11, 362, 512)
        self.uptech.CDS_SetAngle(12, 662, 512)
        time.sleep(1.5)
        self.uptech.CDS_SetAngle(5, 312, 812)
        self.uptech.CDS_SetAngle(8, 712, 812)
        time.sleep(0.5)
        self.uptech.CDS_SetAngle(3, 512, 512)
        self.uptech.CDS_SetAngle(4, 75, 512)
        self.uptech.CDS_SetAngle(5, 307, 512)
        self.uptech.CDS_SetAngle(6, 512, 512)
        self.uptech.CDS_SetAngle(7, 949, 512)
        self.uptech.CDS_SetAngle(8, 717, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)
        self.move_cmd(-500, -500)
        time.sleep(0.3)
        self.move_cmd(0, 0)
        time.sleep(0.01)

    # 攻击动作A
    def attack_a(self):
        self.uptech.CDS_SetAngle(3, 312, 512)
        self.uptech.CDS_SetAngle(4, 812, 512)
        self.uptech.CDS_SetAngle(5, 412, 512)
        self.uptech.CDS_SetAngle(6, 812, 512)
        self.uptech.CDS_SetAngle(7, 212, 512)
        self.uptech.CDS_SetAngle(8, 612, 512)
        self.uptech.CDS_SetAngle(11, 512, 512)
        self.uptech.CDS_SetAngle(12, 512, 512)
        time.sleep(2)
        self.uptech.CDS_SetAngle(3, 152, 512)
        self.uptech.CDS_SetAngle(4, 712, 512)
        self.uptech.CDS_SetAngle(5, 312, 512)
        self.uptech.CDS_SetAngle(6, 862, 512)
        self.uptech.CDS_SetAngle(7, 312, 512)
        self.uptech.CDS_SetAngle(8, 712, 512)
        self.uptech.CDS_SetAngle(11, 522, 512)
        self.uptech.CDS_SetAngle(12, 502, 512)
        time.sleep(1)
        self.move_cmd(300, 300)
        time.sleep(1)
        self.move_cmd(0, 0)

    # 攻击动作B
    def attack_b(self):
        self.uptech.CDS_SetAngle(3, 122, 512)
        self.uptech.CDS_SetAngle(4, 512, 1000)
        self.uptech.CDS_SetAngle(5, 512, 1000)
        self.uptech.CDS_SetAngle(6, 892, 512)
        self.uptech.CDS_SetAngle(7, 512, 1000)
        self.uptech.CDS_SetAngle(8, 512, 1000)
        self.uptech.CDS_SetAngle(11, 522, 512)
        self.uptech.CDS_SetAngle(12, 502, 512)
        time.sleep(2)

    # 攻击能量块的动作
    def attack_tag(self):
        self.uptech.CDS_SetAngle(6, 700, 512)
        self.uptech.CDS_SetAngle(7, 300, 1000)
        self.uptech.CDS_SetAngle(8, 400, 1000)
        time.sleep(1)
        self.uptech.CDS_SetAngle(7, 512, 1000)
        self.uptech.CDS_SetAngle(8, 521, 1000)
        time.sleep(2)
    
    # 上台前的动作
    def start_pose(self):
        # 摆个Pose
        self.uptech.CDS_SetAngle(3, 102, 512)
        self.uptech.CDS_SetAngle(6, 922, 512)
        self.uptech.CDS_SetAngle(11, 922, 512)
        self.uptech.CDS_SetAngle(12, 102, 512)        



