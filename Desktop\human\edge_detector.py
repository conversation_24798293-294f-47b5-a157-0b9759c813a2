import sys
sys.path.append("..")

from uptech import UpTech
import time

class EdgeDetector:
    def __init__(self):
        self.uptech = UpTech()
        self.uptech.ADC_IO_Open()
    
    # 是否跌倒检测, 4号ADC接口，连接倾角传感器
    def angle_detect(self):
        ad_mpu6050 = self.uptech.ADC_Get_Channel(4)
        print(ad_mpu6050)
        # 没有倒
        if 1428 < ad_mpu6050 < 2628:
            return 1
        # 前倾
        elif ad_mpu6050 < 1200:
            return 2
        # 后倾
        elif 1800 < ad_mpu6050 < 2600:
            return 3
        # 边缘状态
        else:
            return 0  
